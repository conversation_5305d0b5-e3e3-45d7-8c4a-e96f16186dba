"use client";

import { DocumentSnapshot } from "firebase/firestore";
import { Plus, Trash2 } from "lucide-react";
import Image from "next/image";
import { useCallback, useState } from "react";

import { deleteApp, getApps } from "@/api/app-api";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { App, APP_TYPE_TEXT } from "@/core.constants";
import { usePaginationRequest } from "@/hooks/use-pagination-request";
import { ManageAppModal } from "./manage-app-modal";

type StatusBadgeProps = {
  enabled: boolean;
  variant?: "default" | "inverse";
};

const StatusBadge = ({ enabled, variant = "default" }: StatusBadgeProps) => {
  const isPositive = variant === "default" ? enabled : !enabled;
  const colorClasses = isPositive
    ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
    : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";

  return (
    <span
      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${colorClasses}`}
    >
      {enabled ? "Yes" : "No"}
    </span>
  );
};

export const AppManagement = () => {
  const [apps, setApps] = useState<App[]>([]);
  const [lastDoc, setLastDoc] = useState<DocumentSnapshot | undefined>();
  const [hasMoreItems, setHasMoreItems] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingApp, setEditingApp] = useState<App | undefined>();
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchApps = useCallback(async () => {
    try {
      const result = await getApps(20, lastDoc);
      setApps((prev) => [...prev, ...result.apps]);
      setLastDoc(result.lastDoc);
      setHasMoreItems(result.hasMore);
    } catch (error) {
      console.error("Error fetching apps:", error);
    }
  }, [lastDoc]);

  const initialFetch = useCallback(async () => {
    try {
      const result = await getApps(20);
      setApps(result.apps);
      setLastDoc(result.lastDoc);
      setHasMoreItems(result.hasMore);
    } catch (error) {
      console.error("Error fetching apps:", error);
    }
  }, []);

  const { loadingMarkup, reset } = usePaginationRequest({
    initialRequest: initialFetch,
    fetchRequest: fetchApps,
    hasMoreItems,
  });

  const handleCreateApp = () => {
    setEditingApp(undefined);
    setIsModalOpen(true);
  };

  const handleEditApp = (app: App) => {
    setEditingApp(app);
    setIsModalOpen(true);
  };

  const handleAppSaved = () => {
    setIsModalOpen(false);
    setEditingApp(undefined);
    setApps([]);
    setLastDoc(undefined);
    setHasMoreItems(true);
    reset();
  };

  const handleDeleteApp = async (app: App) => {
    setIsDeleting(true);
    try {
      await deleteApp(app.id);
      // Refresh the list
      setApps([]);
      setLastDoc(undefined);
      setHasMoreItems(true);
      reset();
    } catch (error) {
      console.error("Error deleting app:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">App Management</h1>
        <div className="flex gap-2">
          <Button onClick={handleCreateApp}>
            <Plus className="mr-2 h-4 w-4" />
            Add App
          </Button>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Logo</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Yubikey Support</TableHead>
              <TableHead>PIN Support</TableHead>
              <TableHead>Phone Required</TableHead>
              <TableHead>Backup Available</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {apps.map((app) => (
              <TableRow key={app.id}>
                <TableCell>
                  {app.appLogo ? (
                    <Image
                      src={app.appLogo}
                      alt={`${app.name} logo`}
                      width={32}
                      height={32}
                      className="w-8 h-8 object-contain rounded"
                    />
                  ) : (
                    <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center">
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        No Logo
                      </span>
                    </div>
                  )}
                </TableCell>
                <TableCell className="font-medium">{app.name}</TableCell>
                <TableCell>{APP_TYPE_TEXT[app.type]}</TableCell>

                <TableCell>
                  <StatusBadge enabled={app.yubikeys?.enabled || false} />
                </TableCell>
                <TableCell>
                  <StatusBadge enabled={app.pin?.enabled || false} />
                </TableCell>
                <TableCell>
                  <StatusBadge
                    enabled={app.phoneNumber?.enabled || false}
                    variant="inverse"
                  />
                </TableCell>
                <TableCell>
                  <StatusBadge enabled={app.backup?.enabled || false} />
                </TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditApp(app)}
                      className="cursor-pointer"
                    >
                      Edit
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950 cursor-pointer"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete App</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete &quot;{app.name}
                            &quot;? This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel className="cursor-pointer">
                            Cancel
                          </AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDeleteApp(app)}
                            disabled={isDeleting}
                            className="bg-red-600 hover:bg-red-700 text-white cursor-pointer"
                          >
                            {isDeleting ? "Deleting..." : "Delete"}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {loadingMarkup}

      <ManageAppModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        app={editingApp}
        onSave={handleAppSaved}
      />
    </div>
  );
};
